<template>
    <div class="price-warning-container">
        <!-- 搜索表单 -->
        <search-form @search="handleSearch" @show-change="handleSearchShow" />

        <!-- 状态切换标签和批量操作 -->
        <div class="status-tabs">
            <div class="tabs-container">
                <el-tabs v-model="activeTab" @tab-change="handleTabChange">
                    <el-tab-pane :label="`全部未处理(${statusCounts.ALL})`" name="ALL" />
                    <el-tab-pane :label="`发货超时(${statusCounts.PENDING_SHIPMENT})`" name="PENDING_SHIPMENT" />
                    <el-tab-pane :label="`揽收异常(${statusCounts.PENDING_PICKUP})`" name="PENDING_PICKUP" />
                    <el-tab-pane :label="`已处理(${statusCounts.PROCESSED})`" name="PROCESSED" />
                </el-tabs>

                <!-- 批量操作按钮 -->
                <div class="batch-actions">
                    <el-button type="warning" plain :disabled="selectedItems.length === 0" @click="handleBatchProcess">
                        <el-icon><Check /></el-icon>
                        批量处理
                    </el-button>
                    <el-button type="danger" plain :disabled="selectedItems.length === 0" @click="handleBatchUrgeShipment">
                        <el-icon><Bell /></el-icon>
                        批量催发货
                    </el-button>
                </div>
            </div>
        </div>

        <!-- 表格 -->
        <q-table
            v-model:checked-item="selectedItems"
            :data="tableData"
            :selection="true"
            class="price-warning-table"
            :class="{ 'table-up': !searchFormVisible }"
            style="margin-top: 13px"
        >
            <q-table-column label="供应商" width="120">
                <template #default="{ row }">
                    <div class="supplier-name">{{ row.supplierName }}</div>
                </template>
            </q-table-column>

            <q-table-column label="订单号" width="180">
                <template #default="{ row }">
                    <div class="order-no clickable" @click="handleOrderDetail(row)">{{ row.orderNo }}</div>
                </template>
            </q-table-column>

            <q-table-column label="成交时间" width="150">
                <template #default="{ row }">
                    <div class="order-time">{{ row.orderTime }}</div>
                </template>
            </q-table-column>

            <q-table-column label="商品信息" width="200">
                <template #default="{ row }">
                    <div class="product-info">
                        <div class="product-name">{{ row.productName }}</div>
                    </div>
                </template>
            </q-table-column>
            <q-table-column label="发货时间" width="150">
                <template #default="{ row }">
                    <div class="delivery-time">{{ row.deliveryTime }}</div>
                </template>
            </q-table-column>

            <q-table-column label="预警类型" width="120">
                <template #default="{ row }">
                    <el-tag :type="getNoticeTypeType(row.noticeType)">
                        {{ getNoticeTypeText(row.noticeType) }}
                    </el-tag>
                </template>
            </q-table-column>

            <q-table-column label="预警时间" width="150">
                <template #default="{ row }">
                    <div class="notice-time">{{ row.noticeTime }}</div>
                </template>
            </q-table-column>

            <q-table-column label="预警状态" width="120">
                <template #default="{ row }">
                    <el-tag :type="getNoticeStatusType(row.noticeStatus)">
                        {{ getNoticeStatusText(row.noticeStatus) }}
                    </el-tag>
                </template>
            </q-table-column>

            <q-table-column label="处理状态" width="120">
                <template #default="{ row }">
                    <el-tag :type="getDealStatusType(row.dealStatus)">
                        {{ getDealStatusText(row.dealStatus) }}
                    </el-tag>
                </template>
            </q-table-column>

            <q-table-column label="售后状态" width="120">
                <template #default="{ row }">
                    <el-tag :type="getAfsStatusType(row.afsStatus)">
                        {{ getAfsStatusText(row.afsStatus) }}
                    </el-tag>
                </template>
            </q-table-column>

            <q-table-column label="处理时间" width="150">
                <template #default="{ row }">
                    <div class="deal-time">{{ row.dealTime }}</div>
                </template>
            </q-table-column>

            <q-table-column label="处理人" width="120">
                <template #default="{ row }">
                    <div class="deal-name">{{ row.dealName }}</div>
                </template>
            </q-table-column>

            <q-table-column label="操作" width="250" fixed="right" align="center">
                <template #default="{ row }">
                    <div class="action-buttons">
                        <el-button v-if="row.dealStatus === 'AWAITING_PROCESSING'" type="primary" link size="small" @click="handleSingleProcess(row)">
                            处理
                        </el-button>
                        <el-button v-if="!row.deliveryTime" type="danger" link size="small" @click="handleUrgeShipment(row)"> 催发货 </el-button>
                    </div>
                </template>
            </q-table-column>
        </q-table>

        <!-- 分页 -->
        <div class="pagination-container">
            <better-page-manage
                :page-num="pageConfig.current"
                :page-size="pageConfig.size"
                :total="pageConfig.total"
                @handle-current-change="handleCurrentChange"
                @handle-size-change="handleSizeChange"
            />
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import QTable from '@/components/qszr-core/packages/q-table/QTable'
import QTableColumn from '@/components/qszr-core/packages/q-table/q-table-column.vue'
import BetterPageManage from '@/components/PageManage.vue'
import SearchForm from './components/search-form.vue'
import { ElMessage, ElTag, ElButton, ElTabs, ElTabPane, ElIcon } from 'element-plus'
import { Check, Bell } from '@element-plus/icons-vue'
import type { Records, SearchParams, StatusTypeMap, StatusTextMap } from '@/apis/logistics/types'
import { getLogisticsNoticeList, batchProcessLogisticsNotice, batchUrgeShipment } from '@/apis/logistics'
import { useRouter } from 'vue-router'

// 路由实例
const router = useRouter()

// 响应式数据
const tableData = ref<Records[]>([])
const selectedItems = ref<Records[]>([])
const searchFormVisible = ref(false)
const loading = ref(false)
const activeTab = ref('ALL')

// 各状态统计数量
const statusCounts = reactive({
    ALL: 0,
    PENDING_SHIPMENT: 0,
    PENDING_PICKUP: 0,
    PROCESSED: 0,
})

// 分页配置
const pageConfig = reactive({
    current: 1,
    size: 20,
    total: 0,
})

// 搜索参数
const searchParams = ref<SearchParams>({})

// 状态映射函数
const getNoticeStatusType = (status: string) => {
    const statusMap: StatusTypeMap = {
        AWAITING_NOTICE: 'warning',
        NOTICE_SUCCESS: 'success',
        NOTICE_FAILED: 'danger',
    }
    return statusMap[status] || 'info'
}

const getNoticeStatusText = (status: string) => {
    const statusMap: StatusTextMap = {
        AWAITING_NOTICE: '待处理',
        NOTICED: '已处理',
    }
    return statusMap[status] || status
}

const getDealStatusType = (status: string) => {
    const statusMap: StatusTypeMap = {
        AWAITING_PROCESSING: 'warning',
        PROCESSED: 'success',
        PROCESSING_FAILED: 'danger',
    }
    return statusMap[status] || 'info'
}

const getDealStatusText = (status: string) => {
    const statusMap: StatusTextMap = {
        AWAITING_PROCESSING: '待处理',
        PROCESSED: '已处理',
    }
    return statusMap[status] || status
}

const getAfsStatusType = (status: string) => {
    const statusMap: StatusTypeMap = {
        NONE: 'success',
        REFUND_REQUEST: 'warning',
        REFUND_AGREE: 'info',
        SYSTEM_REFUND_AGREE: 'info',
        REFUND_REJECT: 'danger',
        REFUNDED: 'success',
        RETURN_REFUND_REQUEST: 'warning',
        RETURN_REFUND_AGREE: 'info',
        SYSTEM_RETURN_REFUND_AGREE: 'info',
        RETURN_REFUND_REJECT: 'danger',
        RETURNED_REFUND: 'warning',
        RETURNED_REFUND_CONFIRM: 'info',
        SYSTEM_RETURNED_REFUND_CONFIRM: 'info',
        RETURNED_REFUND_REJECT: 'danger',
        RETURNED_REFUNDED: 'success',
        BUYER_CLOSED: 'info',
        SYSTEM_CLOSED: 'info',
    }
    return statusMap[status] || 'info'
}

const getAfsStatusText = (status: string) => {
    const statusMap: StatusTextMap = {
        NONE: '不处于售后状态',
        REFUND_REQUEST: '退款申请',
        REFUND_AGREE: '退款已同意',
        SYSTEM_REFUND_AGREE: '系统自动同意退款申请',
        REFUND_REJECT: '退款已拒绝',
        REFUNDED: '已退款',
        RETURN_REFUND_REQUEST: '退货退款申请',
        RETURN_REFUND_AGREE: '退货退款已同意',
        SYSTEM_RETURN_REFUND_AGREE: '系统自动同意退货退款申请',
        RETURN_REFUND_REJECT: '退货退款已拒绝',
        RETURNED_REFUND: '退货退款 买家已发货',
        RETURNED_REFUND_CONFIRM: '退货退款 卖家已确认收货',
        SYSTEM_RETURNED_REFUND_CONFIRM: '退货退款 系统自动确认收货',
        RETURNED_REFUND_REJECT: '退货退款 卖家拒绝收货退回 售后关闭',
        RETURNED_REFUNDED: '已退货退款 已完成',
        BUYER_CLOSED: '买家撤销申请',
        SYSTEM_CLOSED: '系统自动关闭',
    }
    return statusMap[status] || status
}

const getNoticeTypeType = (type: string) => {
    const typeMap: StatusTypeMap = {
        PENDING_SHIPMENT: 'warning',
        PENDING_PICKUP: 'info',
    }
    return typeMap[type] || 'info'
}

const getNoticeTypeText = (type: string) => {
    const typeMap: StatusTextMap = {
        PENDING_SHIPMENT: '待发货',
        PENDING_PICKUP: '待揽件',
    }
    return typeMap[type] || type
}

// 事件处理函数
const handleSearch = (params: SearchParams) => {
    console.log('搜索参数:', params)
    searchParams.value = { ...params }
    pageConfig.current = 1
    fetchData()
    initStatusCounts() // 搜索时重新初始化所有状态统计
}

const handleSearchShow = (visible: boolean) => {
    searchFormVisible.value = visible
}

// 处理标签切换
const handleTabChange = (tabName: string) => {
    activeTab.value = tabName
    pageConfig.current = 1
    fetchData()
}

const handleCurrentChange = (page: number) => {
    pageConfig.current = page
    fetchData()
}

const handleSizeChange = (size: number) => {
    pageConfig.size = size
    pageConfig.current = 1
    fetchData()
}

// 处理订单详情
const handleOrderDetail = (row: Records) => {}

// 单个处理（调用和批量处理相同的API）
const handleSingleProcess = async (row: Records) => {
    try {
        // 调用批量处理API，但只传递单个ID
        const params = [row.id]

        const response = await batchProcessLogisticsNotice(params)
        console.log('单个处理响应:', response)

        if (response.code === 200 || response.code === 0) {
            ElMessage.success('已处理成功')

            // 从选中项中移除该项（如果该项在选中列表中）
            selectedItems.value = selectedItems.value.filter((item) => item.id !== row.id)

            // 刷新数据和所有标签页统计
            fetchData()
            initStatusCounts()
        } else {
            ElMessage.error(response.msg || '处理失败')
        }
    } catch (error) {
        console.error('单个处理失败:', error)
        ElMessage.error('处理失败')
    }
}

const handleUrgeShipment = async (row: Records) => {
    try {
        console.log('=== 开始催发货流程 ===')
        console.log('当前时间:', new Date().toLocaleString())
        console.log('订单数据:', row)

        // 调用批量催发货API，但只传递单个ID
        const params = [row.id]

        const response = await batchUrgeShipment(params)
        console.log('单个催发货响应:', response)

        if (response.code === 200 || response.code === 0) {
            ElMessage.success('催发货成功')

            // 从选中项中移除该项（如果该项在选中列表中）
            selectedItems.value = selectedItems.value.filter((item) => item.id !== row.id)

            // 刷新数据和所有标签页统计
            fetchData()
            initStatusCounts()

            // API调用成功后跳转到平台客服页面
            router.push('/customerService')

            console.log('=== 催发货流程完成，已跳转到客服页面 ===')
        } else {
            ElMessage.error(response.msg || '催发货失败')
        }
    } catch (error) {
        console.error('催发货失败:', error)
        ElMessage.error('催发货失败')
    }
}

// 处理批量处理
const handleBatchProcess = async () => {
    if (selectedItems.value.length === 0) {
        ElMessage.warning('请先选择要处理的数据')
        return
    }

    try {
        const params = selectedItems.value.map((item) => item.id)

        const response = await batchProcessLogisticsNotice(params)
        console.log('批量处理响应:', response)

        if (response.code === 200 || response.code === 0) {
            ElMessage.success(`已批量处理 ${selectedItems.value.length} 条数据`)

            // 清空选中项
            selectedItems.value = []

            // 刷新数据和所有标签页统计
            fetchData()
            initStatusCounts()
        } else {
            ElMessage.error(response.msg || '批量处理失败')
        }
    } catch (error) {
        console.error('批量处理失败:', error)
        ElMessage.error('批量处理失败')
    }
}

// 处理批量催发货
const handleBatchUrgeShipment = async () => {
    if (selectedItems.value.length === 0) {
        ElMessage.warning('请先选择要催发货的数据')
        return
    }

    try {
        // 提取选中项的ID数组
        const params = selectedItems.value.map((item) => item.id)

        const response = await batchUrgeShipment(params)
        console.log('批量催发货响应:', response)

        if (response.code === 200 || response.code === 0) {
            ElMessage.success(`已批量催发货 ${selectedItems.value.length} 条数据`)

            // 清空选中项
            selectedItems.value = []

            // 刷新数据和所有标签页统计
            fetchData()
            initStatusCounts()
        } else {
            ElMessage.error(response.msg || '批量催发货失败')
        }
    } catch (error) {
        console.error('批量催发货失败:', error)
        ElMessage.error('批量催发货失败')
    }
}

// 获取数据
const fetchData = async () => {
    loading.value = true
    try {
        const requestParams = {
            ...searchParams.value,
            current: pageConfig.current,
            size: pageConfig.size,
            queryType: activeTab.value,
        }
        console.log('API请求参数:', requestParams)

        // 调用 API 获取数据
        const response = await getLogisticsNoticeList(requestParams)

        if (response.code === 200 || response.code === 0) {
            tableData.value = (response.data as any).records
            pageConfig.total = (response.data as any).total

            // 更新当前状态的统计数量
            statusCounts[activeTab.value as keyof typeof statusCounts] = (response.data as any).total
        } else {
            ElMessage.error(response.msg || '获取数据失败')
        }
    } catch (error) {
        console.error('获取数据失败:', error)
        ElMessage.error('获取数据失败')
    } finally {
        loading.value = false
    }
}

// 初始化所有状态的统计数量
const initStatusCounts = async () => {
    const statusTypes = ['ALL', 'PENDING_SHIPMENT', 'PENDING_PICKUP', 'PROCESSED']

    for (const queryType of statusTypes) {
        try {
            const response = await getLogisticsNoticeList({
                ...searchParams.value,
                current: 1,
                size: 1, // 只需要获取总数
                queryType,
            })
            if (response.code === 200 || response.code === 0) {
                statusCounts[queryType as keyof typeof statusCounts] = (response.data as any)?.total || 0
            }
        } catch (error) {
            console.error(`获取 ${queryType} 状态统计失败:`, error)
        }
    }
}

// 初始化
onMounted(() => {
    fetchData()
    initStatusCounts()
})
</script>

<style lang="scss" scoped>
.logistics-notice-container {
    padding: 20px;
    background: #f5f5f5;
    min-height: calc(100vh - 60px);
}

.logistics-table {
    &.table-up {
        margin-top: 0 !important;
    }
}

.order-info,
.product-info,
.express-info,
.receiver-info,
.deal-info {
    .order-no,
    .product-name,
    .express-company,
    .receiver-mobile,
    .deal-time {
        font-weight: 500;
        margin-bottom: 4px;
    }

    .order-time,
    .express-no,
    .deal-name {
        font-size: 12px;
        color: #666;
    }
}

.action-buttons {
    display: flex;
    flex-direction: row;
    gap: 8px;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;

    .el-button {
        &.is-disabled {
            opacity: 0.4;
            cursor: not-allowed;

            &:hover {
                opacity: 0.4;
            }
        }
    }
}

.pagination-container {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
    padding: 20px;
}

// 修复操作列标题对齐
:deep(.m__table--head th:last-child div) {
    text-align: center !important;
}

// 订单号点击样式
.order-no.clickable {
    color: #409eff;
    cursor: pointer;
    text-decoration: underline;

    &:hover {
        color: #66b1ff;
    }
}

// 状态切换标签样式
.status-tabs {
    margin: 16px 0;
    background: #fff;
    padding: 0 20px;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .tabs-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .batch-actions {
        display: flex;
        gap: 12px;
        align-items: center;

        .el-button {
            &.is-disabled {
                opacity: 0.5;
                cursor: not-allowed;
            }
        }
    }

    :deep(.el-tabs__header) {
        margin: 0;
    }

    :deep(.el-tabs__nav-wrap::after) {
        display: none;
    }

    :deep(.el-tabs__item) {
        padding: 0 20px;
        height: 48px;
        line-height: 48px;
        font-size: 14px;
        color: #666;

        &.is-active {
            color: #409eff;
            font-weight: 500;
        }

        &:hover {
            color: #409eff;
        }
    }

    :deep(.el-tabs__active-bar) {
        background-color: #409eff;
    }
}
</style>
