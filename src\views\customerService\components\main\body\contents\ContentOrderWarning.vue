<!--
 * @description: 订单预警消息组件
 * @Author: AI Assistant
 * @Date: 2025-01-30
-->
<template>
    <div class="message-content order-warning-content" :class="clazz">
        <div class="message-content-direction" :class="clazz"></div>
        <div class="order-warning-message">
            <div class="warning-header">
                <el-icon class="warning-icon"><Warning /></el-icon>
                <span class="warning-title">订单预警</span>
            </div>
            <div class="warning-details">
                <div v-for="(item, index) in warningItems" :key="index" class="warning-item">
                    <div class="product-info">
                        <span class="product-name">{{ item.productName }}</span>
                        <span class="product-num">数量: {{ item.productNum }}</span>
                    </div>
                    <div class="warning-type">
                        <el-tag :type="getWarningTypeColor(item.noticeType)" size="small">
                            {{ getWarningTypeText(item.noticeType) }}
                        </el-tag>
                    </div>
                    <div class="warning-count">
                        <span class="count-text">共 {{ item.total }} 条预警</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, PropType } from 'vue'
import { Warning } from '@element-plus/icons-vue'
import { MessageAndShopAdmin } from '@/views/customerService/types'

/**
 * msg 消息内容
 * isMine 是否是我的消息
 */
const props = defineProps({
    message: {
        type: Object as PropType<MessageAndShopAdmin>,
        required: true,
    },
    isMine: {
        type: Boolean,
        default: false,
    },
})

const clazz = computed(() => (props.isMine ? 'mine' : 'other'))

// 解析订单预警消息内容
const warningItems = computed(() => {
    try {
        if (props.message.message) {
            return JSON.parse(props.message.message)
        }
        return []
    } catch (error) {
        console.error('解析订单预警消息失败:', error)
        return []
    }
})

// 获取预警类型文本
const getWarningTypeText = (noticeType: string) => {
    const typeMap: Record<string, string> = {
        PENDING_SHIPMENT: '发货超时',
        PENDING_PICKUP: '揽收异常',
    }
    return typeMap[noticeType] || noticeType
}

// 获取预警类型颜色
const getWarningTypeColor = (noticeType: string) => {
    const colorMap: Record<string, string> = {
        PENDING_SHIPMENT: 'warning',
        PENDING_PICKUP: 'danger',
    }
    return colorMap[noticeType] || 'info'
}
</script>

<style scoped lang="scss">
.order-warning-content {
    background: #fff;
    border-radius: 8px;
    padding: 12px;
    max-width: 400px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    &.mine {
        background: #e3f2fd;
        margin-left: auto;

        .message-content-direction {
            right: -8px;
            border-left-color: #e3f2fd;
        }
    }

    &.other {
        background: #fff;
        margin-right: auto;

        .message-content-direction {
            left: -8px;
            border-right-color: #fff;
        }
    }
}

.message-content-direction {
    position: absolute;
    top: 12px;
    width: 0;
    height: 0;
    border: 8px solid transparent;

    &.mine {
        right: -8px;
        border-left-color: #e3f2fd;
        border-right: none;
    }

    &.other {
        left: -8px;
        border-right-color: #fff;
        border-left: none;
    }
}

.order-warning-message {
    .warning-header {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        padding-bottom: 8px;
        border-bottom: 1px solid #f0f0f0;

        .warning-icon {
            color: #ff9800;
            margin-right: 6px;
            font-size: 16px;
        }

        .warning-title {
            font-weight: 600;
            color: #333;
            font-size: 14px;
        }
    }

    .warning-details {
        .warning-item {
            margin-bottom: 12px;

            &:last-child {
                margin-bottom: 0;
            }

            .product-info {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 6px;

                .product-name {
                    font-weight: 500;
                    color: #333;
                    font-size: 13px;
                    flex: 1;
                    margin-right: 8px;
                }

                .product-num {
                    color: #666;
                    font-size: 12px;
                }
            }

            .warning-type {
                margin-bottom: 4px;
            }

            .warning-count {
                .count-text {
                    color: #999;
                    font-size: 12px;
                }
            }
        }
    }
}
</style>
